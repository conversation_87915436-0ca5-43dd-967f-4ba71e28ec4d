from astrbot.api.event import filter
from astrbot.api.star import Context, <PERSON>, register
from astrbot.core import AstrBotConfig
from astrbot.core.platform import AstrMessageEvent
from astrbot.core.star.filter.event_message_type import EventMessageType

from .config import MemeConfig
from .core import MemeManager
from .handlers import TemplateHandlers, GenerationHandler, AdminHandlers
from .utils import PermissionUtils


@register(
    "astrbot_plugin_meme_generator",
    "SodaSizzle",
    "高性能表情包生成器 - 支持多种模板和智能参数识别",
    "v1.1.0",
    "http://127.0.0.1:3000/SodaSizzle/astrbot_plugin_meme_generator",
)
class MemeGeneratorPlugin(Star):
    def __init__(self, context: Context, config: AstrBotConfig):
        super().__init__(context)

        # 初始化配置管理器
        self.meme_config = MemeConfig(config)

        # 初始化核心管理器
        self.meme_manager = MemeManager(self.meme_config)

        # 初始化命令处理器
        self.template_handlers = TemplateHandlers(self.meme_manager, self.meme_config)
        self.generation_handler = GenerationHandler(self.meme_manager)
        self.admin_handlers = AdminHandlers(self.meme_config)

    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口 - 清理资源"""
        await self.cleanup()
        return False  # 不抑制异常

    async def cleanup(self):
        """清理资源"""
        try:
            # 停止缓存清理任务
            await self.meme_manager.cache_manager.stop_cleanup_task()
        except Exception as e:
            from astrbot import logger
            logger.error(f"清理缓存管理器时出错: {e}")



    @filter.command("todo")
    async def custom_t2i_tmpl(self, event: AstrMessageEvent):

        # 自定义的 Jinja2 模板，支持 CSS
        TMPL = '''
            <div style="font-size: 32px;">
            <h1 style="color: black">Todo List</h1>

            <ul>
            {% for item in items %}
                <li>{{ item }}</li>
            {% endfor %}
            </div>
            '''

        url = await self.html_render(TMPL, {"items": ["吃饭", "睡觉", "玩原神"]})  # 第二个参数是 Jinja2 的渲染数据
        yield event.image_result(url)

    @filter.command("image")  # 注册一个 /image 指令，接收 text 参数。
    async def on_aiocqhttp(self, event: AstrMessageEvent, text: str):
        url = await self.text_to_image(text)  # text_to_image() 是 Star 类的一个方法。
        # path = await self.text_to_image(text, return_url = False) # 如果你想保存图片到本地
        yield event.image_result(url)

    @filter.command("表情列表", alias={"图片列表", "模板列表"})
    async def template_list(self, event: AstrMessageEvent):
        """查看所有可用的表情包模板"""
        # 检查插件是否启用
        if not self.meme_config.is_plugin_enabled():
            if PermissionUtils.is_bot_admin(event):
                yield event.plain_result(PermissionUtils.get_plugin_disabled_message())
            return

        async for result in self.template_handlers.handle_template_list(event):
            yield result


    @filter.command("模板信息", alias={"表情信息", "查看模板"})
    async def template_info(
        self, event: AstrMessageEvent, keyword: str | int | None = None
    ):
        """查看指定表情包模板的详细信息"""
        # 检查插件是否启用
        if not self.meme_config.is_plugin_enabled():
            if PermissionUtils.is_bot_admin(event):
                yield event.plain_result(PermissionUtils.get_plugin_disabled_message())
            return

        async for result in self.template_handlers.handle_template_info(event, keyword):
            yield result

    @filter.permission_type(filter.PermissionType.ADMIN)
    @filter.command("禁用模板")
    async def disable_template(
        self, event: AstrMessageEvent, template_name: str | None = None
    ):
        """禁用指定的表情包模板（仅限Bot管理员）"""
        async for result in self.template_handlers.handle_disable_template(event, template_name):
            yield result

    @filter.permission_type(filter.PermissionType.ADMIN)
    @filter.command("启用模板")
    async def enable_template(
        self, event: AstrMessageEvent, template_name: str | None = None
    ):
        """启用指定的表情包模板（仅限Bot管理员）"""
        async for result in self.template_handlers.handle_enable_template(event, template_name):
            yield result

    @filter.permission_type(filter.PermissionType.ADMIN)
    @filter.command("禁用列表")
    async def list_disabled(self, event: AstrMessageEvent):
        """查看被禁用的模板列表（仅限Bot管理员）"""
        async for result in self.template_handlers.handle_list_disabled(event):
            yield result

    @filter.permission_type(filter.PermissionType.ADMIN)
    @filter.command("启用表情包", alias={"开启表情包", "启用插件"})
    async def enable_plugin(self, event: AstrMessageEvent):
        """启用表情包生成功能（仅限Bot管理员）"""
        async for result in self.admin_handlers.handle_enable_plugin(event):
            yield result

    @filter.permission_type(filter.PermissionType.ADMIN)
    @filter.command("禁用表情包", alias={"关闭表情包", "禁用插件"})
    async def disable_plugin(self, event: AstrMessageEvent):
        """禁用表情包生成功能（仅限Bot管理员）"""
        async for result in self.admin_handlers.handle_disable_plugin(event):
            yield result

    @filter.permission_type(filter.PermissionType.ADMIN)
    @filter.command("表情包状态", alias={"插件状态"})
    async def plugin_status(self, event: AstrMessageEvent):
        """查看表情包插件状态（仅限Bot管理员）"""
        async for result in self.admin_handlers.handle_plugin_status(event):
            yield result

    @filter.permission_type(filter.PermissionType.ADMIN)
    @filter.command("表情包信息", alias={"插件信息"})
    async def plugin_info(self, event: AstrMessageEvent):
        """查看表情包插件详细信息（仅限Bot管理员）"""
        async for result in self.admin_handlers.handle_plugin_info(event):
            yield result





    @filter.event_message_type(EventMessageType.ALL)
    async def generate_meme(self, event: AstrMessageEvent):
        """
        表情包生成主流程处理器。

        功能特性：
        - 智能关键词匹配和识别
        - 多源参数提取（消息文本、@用户、上传图片）
        - 引用消息内容解析
        - 自动补全缺失参数（头像、昵称等）
        """
        # 检查插件是否启用
        if not self.meme_config.is_plugin_enabled():
            # 插件被禁用时不响应普通用户，但Bot管理员可以看到提示
            if PermissionUtils.is_bot_admin(event):
                yield event.plain_result(PermissionUtils.get_plugin_disabled_message())
            return

        async for result in self.generation_handler.handle_generate_meme(event):
            yield result


