"""模板相关命令处理器"""

from astrbot.core.platform import AstrMessageEvent
import astrbot.core.message.components as Comp
from ..core import MemeManager
from ..config import MemeConfig


class TemplateHandlers:
    """模板相关命令处理器"""
    
    def __init__(self, meme_manager: MemeManager, config: MemeConfig):
        self.meme_manager = meme_manager
        self.config = config
    
    async def handle_template_list(self, event: AstrMessageEvent):
        """处理表情列表命令"""
        # 尝试生成HTML图片
        output = await self.meme_manager.generate_template_list()
        if output:
            yield event.chain_result([Comp.Image.fromBytes(output)])
            return

        # 如果HTML渲染失败，使用美化的文本版本
        try:
            formatted_list = await self._generate_beautiful_text_list()
            yield event.plain_result(formatted_list)
        except Exception as e:
            yield event.plain_result("表情包列表生成失败")
    
    async def handle_template_info(self, event: AstrMessageEvent, keyword: str | int | None = None):
        """处理模板信息命令"""
        if not keyword:
            yield event.plain_result("请指定要查看的模板关键词")
            return

        keyword = str(keyword)

        # 尝试生成HTML图片
        image_data = await self.meme_manager.generate_template_info_image(keyword)
        if image_data:
            yield event.chain_result([Comp.Image.fromBytes(image_data)])
            return

        # 如果HTML渲染失败，回退到文本模式
        template_info = await self.meme_manager.get_template_info(keyword)
        if not template_info:
            yield event.plain_result("未找到相关模板")
            return

        # 构建信息文本
        meme_info = self._build_template_info_text(template_info)
        yield event.plain_result(meme_info)
    
    async def handle_disable_template(self, event: AstrMessageEvent, template_name: str | None = None):
        """处理禁用模板命令"""
        if not template_name:
            yield event.plain_result("请指定要禁用的模板名称")
            return

        if not await self.meme_manager.template_manager.keyword_exists(template_name):
            yield event.plain_result(f"模板 {template_name} 不存在")
            return

        if self.config.is_template_disabled(template_name):
            yield event.plain_result(f"模板 {template_name} 已被禁用")
            return

        if self.config.disable_template(template_name):
            yield event.plain_result(f"✅ 已禁用模板: {template_name}")
        else:
            yield event.plain_result(f"❌ 禁用模板失败: {template_name}")
    
    async def handle_enable_template(self, event: AstrMessageEvent, template_name: str | None = None):
        """处理启用模板命令"""
        if not template_name:
            yield event.plain_result("请指定要启用的模板名称")
            return

        if not await self.meme_manager.template_manager.keyword_exists(template_name):
            yield event.plain_result(f"模板 {template_name} 不存在")
            return

        if not self.config.is_template_disabled(template_name):
            yield event.plain_result(f"模板 {template_name} 未被禁用")
            return

        if self.config.enable_template(template_name):
            yield event.plain_result(f"✅ 已启用模板: {template_name}")
        else:
            yield event.plain_result(f"❌ 启用模板失败: {template_name}")
    
    async def handle_list_disabled(self, event: AstrMessageEvent):
        """处理禁用列表命令"""
        # 尝试生成HTML图片
        image_data = await self.meme_manager.generate_disabled_list_image()
        if image_data:
            yield event.chain_result([Comp.Image.fromBytes(image_data)])
            return

        # 如果HTML渲染失败，回退到文本模式
        disabled_templates = self.config.get_disabled_templates()

        if not disabled_templates:
            yield event.plain_result("📋 当前没有禁用的模板")
            return

        # 格式化展示禁用列表
        formatted_text = self._format_template_list(
            disabled_templates,
            title="🔒 禁用模板列表",
            empty_message="当前没有禁用的模板"
        )

        yield event.plain_result(formatted_text)

    def _format_template_list(self, templates: list, title: str, empty_message: str, items_per_page: int = 20) -> str:
        """格式化模板列表展示"""
        if not templates:
            return f"🎉 {title}\n{'=' * 20}\n✨ {empty_message}\n\n💡 所有表情包模板都可以正常使用！"

        # 计算总页数
        total_items = len(templates)
        total_pages = (total_items + items_per_page - 1) // items_per_page

        # 构建格式化文本
        result = f"🔒 {title}\n"
        result += "=" * 25 + "\n"
        result += f"📊 禁用模板数: {total_items} 个\n"

        if total_pages > 1:
            result += f"📄 分页显示 (每页 {items_per_page} 个，共 {total_pages} 页)\n"

        result += "─" * 25 + "\n"

        # 显示第一页内容
        page_templates = templates[:items_per_page]

        # 计算列宽（用于对齐）
        max_index_width = len(str(len(page_templates)))

        for i, template in enumerate(page_templates, 1):
            # 格式化编号，右对齐
            index_str = f"{i:>{max_index_width}}"
            result += f"🚫 {index_str}. {template}\n"

        if total_pages > 1:
            result += "─" * 25 + "\n"
            result += f"💡 当前显示第 1/{total_pages} 页"
            if total_items > items_per_page:
                remaining = total_items - items_per_page
                result += f"，还有 {remaining} 个模板未显示"

        result += f"\n\n⚙️ 管理员可使用 '/启用模板 模板名' 重新启用"
        return result

    def _build_template_info_text(self, template_info: dict) -> str:
        """构建美化的模板信息文本"""
        result = "🎭 表情包模板详情\n"
        result += "=" * 25 + "\n"

        # 基本信息
        if template_info["name"]:
            result += f"📝 模板名称: {template_info['name']}\n"

        if template_info["keywords"]:
            keywords = template_info['keywords']
            if isinstance(keywords, list):
                keywords_str = ', '.join(keywords)
            else:
                keywords_str = str(keywords)
            result += f"🔑 触发关键词: {keywords_str}\n"

        result += "\n📊 参数要求\n"
        result += "─" * 15 + "\n"

        # 图片要求
        max_images = template_info["max_images"]
        min_images = template_info["min_images"]
        if max_images > 0:
            if min_images == max_images:
                result += f"🖼️ 所需图片: {min_images} 张\n"
            else:
                result += f"🖼️ 所需图片: {min_images}~{max_images} 张\n"
        else:
            result += f"🖼️ 所需图片: 无需图片\n"

        # 文本要求
        max_texts = template_info["max_texts"]
        min_texts = template_info["min_texts"]
        if max_texts > 0:
            if min_texts == max_texts:
                result += f"💬 所需文本: {min_texts} 段\n"
            else:
                result += f"💬 所需文本: {min_texts}~{max_texts} 段\n"
        else:
            result += f"💬 所需文本: 无需文本\n"

        # 默认文本
        if template_info["default_texts"]:
            default_texts = template_info["default_texts"]
            if isinstance(default_texts, list) and default_texts:
                result += f"\n📋 默认文本:\n"
                for i, text in enumerate(default_texts, 1):
                    result += f"   {i}. {text}\n"

        # 标签
        if template_info["tags"]:
            tags = template_info["tags"]
            if isinstance(tags, list):
                tags_str = ', '.join(tags)
            else:
                tags_str = str(tags)
            result += f"\n🏷️ 分类标签: {tags_str}\n"

        # 使用示例
        result += "\n💡 使用示例\n"
        result += "─" * 15 + "\n"

        if template_info["keywords"]:
            first_keyword = template_info["keywords"][0] if isinstance(template_info["keywords"], list) else str(template_info["keywords"]).split(',')[0].strip()

            if max_images > 0 and max_texts > 0:
                result += f"📤 {first_keyword} [上传{min_images}张图片] 文本内容\n"
            elif max_images > 0:
                result += f"📤 {first_keyword} [上传{min_images}张图片]\n"
            elif max_texts > 0:
                result += f"📤 {first_keyword} 文本内容\n"
            else:
                result += f"📤 {first_keyword}\n"

            if max_texts > 1:
                result += f"📤 {first_keyword} 第一段文本 第二段文本\n"

        result += "\n🔍 使用 '/表情列表' 查看所有模板"

        return result

    async def _generate_beautiful_text_list(self) -> str:
        """生成美化的文本版表情包列表"""
        try:
            # 获取所有模板
            all_memes = await self.meme_manager.template_manager.get_all_memes()

            # 按分类整理模板
            categories = {}
            uncategorized = []

            for meme in all_memes:
                info = meme.info
                if info.tags:
                    for tag in info.tags:
                        if tag not in categories:
                            categories[tag] = []
                        categories[tag].append({
                            'name': meme.key,
                            'keywords': list(info.keywords),
                            'min_images': info.params.min_images,
                            'max_images': info.params.max_images,
                            'min_texts': info.params.min_texts,
                            'max_texts': info.params.max_texts,
                        })
                        break  # 只放在第一个分类中
                else:
                    uncategorized.append({
                        'name': meme.key,
                        'keywords': list(info.keywords),
                        'min_images': info.params.min_images,
                        'max_images': info.params.max_images,
                        'min_texts': info.params.min_texts,
                        'max_texts': info.params.max_texts,
                    })

            # 构建美化的文本
            result = "🎭 表情包模板列表\n"
            result += "=" * 30 + "\n"

            # 统计信息
            total_memes = len(all_memes)
            enabled_memes = len([m for m in all_memes if not self.config.is_template_disabled(m.key)])
            result += f"📊 总计: {total_memes} 个模板 | 可用: {enabled_memes} 个\n"
            result += f"📂 分类: {len(categories)} 个\n"
            result += "=" * 30 + "\n\n"

            # 显示分类模板
            for category, memes in sorted(categories.items()):
                result += f"📁 {category} ({len(memes)}个)\n"
                result += "─" * 20 + "\n"

                for i, meme in enumerate(memes[:10], 1):  # 每个分类最多显示10个
                    disabled = self.config.is_template_disabled(meme['name'])
                    status = "🔒" if disabled else "✅"

                    # 格式化参数信息
                    params = []
                    if meme['max_images'] > 0:
                        if meme['min_images'] == meme['max_images']:
                            params.append(f"{meme['min_images']}图")
                        else:
                            params.append(f"{meme['min_images']}-{meme['max_images']}图")

                    if meme['max_texts'] > 0:
                        if meme['min_texts'] == meme['max_texts']:
                            params.append(f"{meme['min_texts']}文")
                        else:
                            params.append(f"{meme['min_texts']}-{meme['max_texts']}文")

                    param_str = f"({'/'.join(params)})" if params else ""

                    result += f"{status} {i:2d}. {meme['name']} {param_str}\n"
                    result += f"     关键词: {', '.join(meme['keywords'][:3])}{'...' if len(meme['keywords']) > 3 else ''}\n"

                if len(memes) > 10:
                    result += f"     ... 还有 {len(memes) - 10} 个模板\n"
                result += "\n"

            # 显示未分类模板
            if uncategorized:
                result += f"📝 其他模板 ({len(uncategorized)}个)\n"
                result += "─" * 20 + "\n"

                for i, meme in enumerate(uncategorized[:15], 1):  # 最多显示15个
                    disabled = self.config.is_template_disabled(meme['name'])
                    status = "🔒" if disabled else "✅"

                    # 格式化参数信息
                    params = []
                    if meme['max_images'] > 0:
                        if meme['min_images'] == meme['max_images']:
                            params.append(f"{meme['min_images']}图")
                        else:
                            params.append(f"{meme['min_images']}-{meme['max_images']}图")

                    if meme['max_texts'] > 0:
                        if meme['min_texts'] == meme['max_texts']:
                            params.append(f"{meme['min_texts']}文")
                        else:
                            params.append(f"{meme['min_texts']}-{meme['max_texts']}文")

                    param_str = f"({'/'.join(params)})" if params else ""

                    result += f"{status} {i:2d}. {meme['name']} {param_str}\n"
                    result += f"     关键词: {', '.join(meme['keywords'][:3])}{'...' if len(meme['keywords']) > 3 else ''}\n"

                if len(uncategorized) > 15:
                    result += f"     ... 还有 {len(uncategorized) - 15} 个模板\n"
                result += "\n"

            # 添加使用说明
            result += "💡 使用说明\n"
            result += "─" * 20 + "\n"
            result += "• 发送 '关键词 + 图片/文字' 生成表情包\n"
            result += "• 使用 '/模板信息 关键词' 查看详细信息\n"
            result += "• ✅=可用 🔒=已禁用\n"

            return result

        except Exception as e:
            return f"生成表情包列表时出错: {str(e)}"
