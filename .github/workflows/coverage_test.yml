name: Run tests and upload coverage

on:
  push:
    branches:
      - master
    paths-ignore:
      - 'README.md'
      - 'changelogs/**'
      - 'dashboard/**'
  pull_request:
  workflow_dispatch:

jobs:
  test:
    name: Run tests and collect coverage
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v5
      
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pytest pytest-asyncio pytest-cov
          pip install --editable .

      - name: Run tests
        run: |
          mkdir -p data/plugins
          mkdir -p data/config
          mkdir -p data/temp
          export TESTING=true
          export ZHIPU_API_KEY=${{ secrets.OPENAI_API_KEY }}
          pytest --cov=. -v -o log_cli=true -o log_level=DEBUG

      - name: Upload results to Codecov
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
