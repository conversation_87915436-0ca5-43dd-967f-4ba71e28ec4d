"""表情包管理器模块"""

import asyncio
from typing import List, Optional
from meme_generator.tools import MemeProperties, MemeSortBy, render_meme_list
from meme_generator.resources import check_resources_in_background
from astrbot import logger
from astrbot.core.platform import AstrMessageEvent
import astrbot.core.message.components as Comp
from jinja2 import Template
import os

from .template_manager import TemplateManager
from .param_collector import ParamCollector
from .image_generator import ImageGenerator
from ..config import MemeConfig
from ..utils import ImageUtils, CooldownManager, AvatarCache, NetworkUtils, CacheManager


class MemeManager:
    """表情包管理器 - 核心业务逻辑"""
    
    def __init__(self, config: MemeConfig):
        self.config = config
        self.template_manager = TemplateManager()
        self.image_generator = ImageGenerator()
        self.cooldown_manager = CooldownManager(config.cooldown_seconds)

        # 初始化头像缓存和网络工具
        self.avatar_cache = AvatarCache(
            cache_expire_hours=config.cache_expire_hours,
            enable_cache=config.enable_avatar_cache,
            cache_dir="data/cache/meme_avatars"  # 存储到外部data目录
        )
        self.network_utils = NetworkUtils(self.avatar_cache)

        # 初始化缓存管理器，使用配置的缓存过期时间
        self.cache_manager = CacheManager(
            self.avatar_cache,
            cleanup_interval_hours=config.cache_expire_hours
        )

        # 初始化参数收集器（传入网络工具）
        self.param_collector = ParamCollector(self.network_utils)

        # 初始化资源检查
        if config.resource_check:
            logger.info("正在检查表情包资源文件...")
            # 异步启动资源检查，并在完成后刷新模板
            asyncio.create_task(self._check_resources_and_refresh())

        # 启动缓存清理任务
        if config.enable_avatar_cache:
            try:
                loop = asyncio.get_event_loop()
                loop.create_task(self.cache_manager.start_cleanup_task())
            except RuntimeError:
                # 如果没有运行的事件循环，稍后启动
                pass

    async def _check_resources_and_refresh(self):
        """检查资源并在完成后刷新模板"""
        try:
            # 在线程池中执行资源检查（因为它是同步的）
            await asyncio.to_thread(check_resources_in_background)
            logger.info("资源检查完成，正在刷新模板列表...")
            # 刷新模板列表
            await self.template_manager.refresh_templates()
            logger.info("模板列表刷新完成")
        except Exception as e:
            logger.error(f"资源检查或模板刷新失败: {e}")
    
    async def generate_template_list(self) -> bytes | None:
        """
        生成表情包模板列表图片 - 使用HTML模板渲染

        Returns:
            模板列表图片字节数据，失败返回None
        """
        try:
            # 获取所有模板
            all_memes = await self.template_manager.get_all_memes()

            # 准备模板数据
            memes_data = []
            categories = set()

            for meme in all_memes:
                info = meme.info
                params = info.params

                # 收集分类标签
                for tag in info.tags:
                    categories.add(tag)

                meme_data = {
                    'name': meme.key,
                    'keywords': list(info.keywords),
                    'min_images': params.min_images,
                    'max_images': params.max_images,
                    'min_texts': params.min_texts,
                    'max_texts': params.max_texts,
                    'tags': list(info.tags),
                }
                memes_data.append(meme_data)

            # 统计信息
            total_memes = len(memes_data)
            enabled_memes = len([m for m in memes_data if not self.config.is_template_disabled(m['name'])])

            # 渲染HTML模板
            template_data = {
                'memes': memes_data,
                'categories': sorted(list(categories)),
                'total_memes': total_memes,
                'enabled_memes': enabled_memes,
            }

            return await self._render_html_template('simple_meme_list.html', template_data)

        except Exception as e:
            logger.error(f"生成模板列表失败: {e}")
            return None
    
    async def get_template_info(self, keyword: str) -> Optional[dict]:
        """
        获取模板详细信息

        Args:
            keyword: 模板关键词

        Returns:
            模板信息字典，未找到返回None
        """
        if not await self.template_manager.keyword_exists(keyword):
            return None

        meme = await self.template_manager.find_meme(keyword)
        if not meme:
            return None

        info = meme.info
        params = info.params

        template_info = {
            "name": meme.key,
            "keywords": list(info.keywords),
            "min_images": params.min_images,
            "max_images": params.max_images,
            "min_texts": params.min_texts,
            "max_texts": params.max_texts,
            "default_texts": list(params.default_texts) if params.default_texts else [],
            "tags": list(info.tags),
        }

        return template_info

    async def generate_template_info_image(self, keyword: str) -> bytes | None:
        """
        生成模板信息图片

        Args:
            keyword: 模板关键词

        Returns:
            模板信息图片字节数据，失败返回None
        """
        template_info = await self.get_template_info(keyword)
        if not template_info:
            return None

        try:
            return await self._render_html_template('meme_info.html', {'meme': template_info})
        except Exception as e:
            logger.error(f"生成模板信息图片失败: {e}")
            return None

    async def _render_html_template(self, template_name: str, data: dict) -> bytes | None:
        """
        渲染HTML模板为图片

        Args:
            template_name: 模板文件名
            data: 模板数据

        Returns:
            渲染后的图片字节数据，失败返回None
        """
        try:
            # 获取模板文件路径
            template_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'templates')
            template_path = os.path.join(template_dir, template_name)

            if not os.path.exists(template_path):
                logger.error(f"模板文件不存在: {template_path}")
                return None

            # 读取模板文件
            with open(template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()

            # 使用Jinja2渲染模板
            template = Template(template_content)
            html_content = template.render(**data)

            # 使用AstrBot的text_to_image静态方法
            try:
                from astrbot.api.star import Star

                logger.info(f"开始HTML转图片，HTML长度: {len(html_content)}")

                # 直接使用静态方法转换HTML为图片
                image_path = await Star.text_to_image(html_content, return_url=False)

                logger.info(f"HTML转图片完成，路径: {image_path}")

                if image_path and os.path.exists(image_path):
                    file_size = os.path.getsize(image_path)
                    logger.info(f"图片文件大小: {file_size} bytes")

                    if file_size > 0:
                        with open(image_path, 'rb') as f:
                            image_data = f.read()

                        # 清理临时文件
                        try:
                            os.remove(image_path)
                        except:
                            pass

                        logger.info(f"成功读取图片数据: {len(image_data)} bytes")
                        return image_data
                    else:
                        logger.warning("生成的图片文件为空")
                else:
                    logger.warning(f"图片文件不存在或路径为空: {image_path}")

            except Exception as star_error:
                logger.error(f"HTML转图片失败: {star_error}")
                # 回退到文本模式
                return None

            return None

        except Exception as e:
            logger.error(f"HTML模板渲染失败: {e}")
            return None

    async def generate_disabled_list_image(self) -> bytes | None:
        """
        生成禁用模板列表图片

        Returns:
            禁用列表图片字节数据，失败返回None
        """
        try:
            disabled_templates = self.config.get_disabled_templates()
            all_memes = await self.template_manager.get_all_memes()
            total_count = len(all_memes)
            disabled_count = len(disabled_templates)

            template_data = {
                'disabled_templates': disabled_templates,
                'disabled_count': disabled_count,
                'total_count': total_count,
            }

            return await self._render_html_template('disabled_list.html', template_data)

        except Exception as e:
            logger.error(f"生成禁用列表失败: {e}")
            return None

    async def generate_meme(self, event: AstrMessageEvent) -> Optional[bytes]:
        """
        生成表情包主流程

        Args:
            event: 消息事件

        Returns:
            生成的表情包图片字节数据，失败返回None
        """
        # 检查用户冷却
        user_id = event.get_sender_id()
        if self.cooldown_manager.is_user_in_cooldown(user_id):
            # 用户在冷却期内，静默返回
            return None

        # 提取消息内容
        message_str = event.get_message_str()
        if not message_str:
            return None
        
        # 查找关键词
        keyword = await self.template_manager.find_keyword(message_str)
        if not keyword:
            return None

        if self.config.is_template_disabled(keyword):
            return None

        # 查找模板
        meme = await self.template_manager.find_meme(keyword)
        if not meme:
            return None
        
        # 收集生成参数
        meme_images, texts, options = await self.param_collector.collect_params(event, keyword, meme)
        
        # 生成表情包
        image: bytes = await self.image_generator.generate_image(
            meme, meme_images, texts, options, self.config.generation_timeout
        )
        
        # 自动压缩处理
        try:
            compressed = ImageUtils.compress_image(image)
            if compressed:
                image = compressed
        except Exception:
            pass  # 压缩失败时使用原图

        # 记录用户使用时间
        self.cooldown_manager.record_user_use(user_id)

        return image
