"""模板管理模块"""

from typing import List, Optional
from meme_generator import Meme, get_memes


class TemplateManager:
    """表情包模板管理器"""
    
    def __init__(self):
        self.memes: List[Meme] = get_memes()
        self.meme_keywords = [
            keyword for meme in self.memes for keyword in meme.info.keywords
        ]
    
    def find_meme(self, keyword: str) -> Optional[Meme]:
        """
        根据关键词查找表情包模板
        
        Args:
            keyword: 关键词
            
        Returns:
            找到的表情包模板，未找到返回None
        """
        for meme in self.memes:
            if keyword == meme.key or any(k == keyword for k in meme.info.keywords):
                return meme
        return None
    
    def find_keyword(self, message_str: str, smart_match: bool = False) -> Optional[str]:
        """
        从消息中查找匹配的关键词
        
        Args:
            message_str: 消息字符串
            smart_match: 是否启用智能匹配
            
        Returns:
            匹配的关键词，未找到返回None
        """
        if smart_match:
            # 智能匹配：检查关键词是否在消息字符串中
            return next((k for k in self.meme_keywords if k in message_str), None)
        else:
            # 精确匹配：检查关键词是否等于消息字符串的第一个单词
            words = message_str.split()
            if not words:
                return None
            return next((k for k in self.meme_keywords if k == words[0]), None)
    
    def get_all_keywords(self) -> List[str]:
        """获取所有关键词"""
        return self.meme_keywords.copy()
    
    def get_all_memes(self) -> List[Meme]:
        """获取所有表情包模板"""
        return self.memes.copy()
    
    def keyword_exists(self, keyword: str) -> bool:
        """检查关键词是否存在"""
        return keyword in self.meme_keywords
